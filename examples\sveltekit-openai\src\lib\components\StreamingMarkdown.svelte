<script lang="ts">
  import { onMount } from 'svelte';
  import * as smd from 'streaming-markdown';

  interface Props {
    content?: string;
    animationDelay?: number;
    fadeInDuration?: number;
  }

  let {
    content = '',
    animationDelay = 20,
    fadeInDuration = 400
  }: Props = $props();

  let containerElement: HTMLElement | undefined = $state();
  let parser: any;
  let previousContent = '';
  let charIndex = 0;

  // 为新添加的文本节点添加动画
  function addAnimationToNewText() {
    if (!containerElement) return;

    const walker = document.createTreeWalker(
      containerElement,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node;
    while (node = walker.nextNode()) {
      const textNode = node as Text;
      const parent = textNode.parentElement;

      if (parent && !parent.classList.contains('animated')) {
        // 标记为已处理
        parent.classList.add('animated');

        // 将文本分割成字符并添加动画
        const text = textNode.textContent || '';
        const fragment = document.createDocumentFragment();

        text.split('').forEach((char) => {
          const span = document.createElement('span');
          span.className = 'fade-in-char';
          span.textContent = char === ' ' ? '\u00A0' : char;
          span.style.animationDelay = `${charIndex * animationDelay}ms`;
          span.style.animationDuration = `${fadeInDuration}ms`;
          fragment.appendChild(span);
          charIndex++;
        });

        parent.replaceChild(fragment, textNode);
      }
    }
  }

  // 处理新增内容
  function processNewContent() {
    if (!parser || !containerElement) return;

    const newContent = content.slice(previousContent.length);
    if (!newContent) return;

    // 写入新的内容块
    smd.parser_write(parser, newContent);
    previousContent = content;

    // 为新内容添加动画
    setTimeout(() => addAnimationToNewText(), 0);
  }

  // 初始化解析器
  function initializeParser() {
    if (!containerElement) return;

    // 清空容器并重置计数器
    containerElement.innerHTML = '';
    charIndex = 0;

    // 使用默认渲染器
    const renderer = smd.default_renderer(containerElement);
    parser = smd.parser(renderer);

    // 如果有初始内容，处理它
    if (content) {
      smd.parser_write(parser, content);
      previousContent = content;
      setTimeout(() => addAnimationToNewText(), 0);
    }
  }

  // 响应内容变化
  $effect(() => {
    if (containerElement) {
      if (content.length < previousContent.length) {
        // 内容被重置或减少，重新初始化
        initializeParser();
      } else {
        // 内容增加，增量处理
        processNewContent();
      }
    }
  });

  onMount(() => {
    if (containerElement) {
      initializeParser();
    }
  });
</script>

<div bind:this={containerElement} class="streaming-markdown">
  <!-- 内容将通过 streaming-markdown 库直接渲染到这里 -->
</div>

<style>
  :global(.streaming-markdown) {
    line-height: 1.6;
  }

  :global(.streaming-markdown h1) {
    font-size: 2em;
    font-weight: bold;
    margin: 0.67em 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }

  :global(.streaming-markdown h2) {
    font-size: 1.5em;
    font-weight: bold;
    margin: 0.83em 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.3em;
  }

  :global(.streaming-markdown h3) {
    font-size: 1.17em;
    font-weight: bold;
    margin: 1em 0;
  }

  :global(.streaming-markdown h4) {
    font-size: 1em;
    font-weight: bold;
    margin: 1.33em 0;
  }

  :global(.streaming-markdown h5) {
    font-size: 0.83em;
    font-weight: bold;
    margin: 1.67em 0;
  }

  :global(.streaming-markdown h6) {
    font-size: 0.67em;
    font-weight: bold;
    margin: 2.33em 0;
  }

  :global(.streaming-markdown p) {
    margin: 1em 0;
  }

  :global(.streaming-markdown ul, .streaming-markdown ol) {
    margin: 1em 0;
    padding-left: 2em;
  }

  :global(.streaming-markdown li) {
    margin: 0.5em 0;
  }

  :global(.streaming-markdown blockquote) {
    margin: 1em 0;
    padding: 0 1em;
    border-left: 4px solid #ddd;
    color: #666;
  }

  :global(.streaming-markdown code) {
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
  }

  :global(.streaming-markdown pre) {
    background-color: #f5f5f5;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin: 1em 0;
  }

  :global(.streaming-markdown pre code) {
    background-color: transparent;
    padding: 0;
  }

  :global(.streaming-markdown table) {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
  }

  :global(.streaming-markdown th, .streaming-markdown td) {
    border: 1px solid #ddd;
    padding: 0.5em;
    text-align: left;
  }

  :global(.streaming-markdown th) {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  :global(.streaming-markdown a) {
    color: #0066cc;
    text-decoration: underline;
  }

  :global(.streaming-markdown a:hover) {
    color: #0052a3;
  }

  :global(.streaming-markdown strong) {
    font-weight: bold;
  }

  :global(.streaming-markdown em) {
    font-style: italic;
  }

  :global(.streaming-markdown hr) {
    border: none;
    border-top: 1px solid #ddd;
    margin: 2em 0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  :global(.fade-in-char) {
    display: inline-block;
    opacity: 0;
    animation: fadeIn forwards;
    line-height: 1.2;
  }
</style>
